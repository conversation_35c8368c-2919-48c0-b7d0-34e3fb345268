plugins {
    id "com.android.application"
    id "org.jetbrains.kotlin.android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode', '1')
def flutterVersionName = localProperties.getProperty('flutter.versionName', '1.0')
 
 def keystoreProperties = new Properties()
 def keystorePropertiesFile = rootProject.file("key.properties")
 if (keystorePropertiesFile.exists()) {
     keystorePropertiesFile.withReader('UTF-8') { reader ->
         keystoreProperties.load(reader)
     }
 }

android {
    namespace "com.example.eye_help_second"
    compileSdk flutter.compileSdkVersion  // Reverting to Flutter-managed compileSdk

    defaultConfig {
        applicationId "com.example.eye_help_second"
        minSdkVersion 26
        targetSdkVersion flutter.targetSdkVersion  // Reverting to Flutter-managed targetSdk
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
    ndkVersion "25.1.8937393"
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17  // Use Java 17 for stability
        targetCompatibility JavaVersion.VERSION_17
    }
 
     signingConfigs {
         release {
             keyAlias keystoreProperties['keyAlias']
             keyPassword keystoreProperties['keyPassword']
             storeFile file(keystoreProperties['storeFile'])
             storePassword keystoreProperties['storePassword']
         }
     }

    kotlinOptions {
        jvmTarget = "17"
    }

    buildTypes {
        release {
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.release
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.9.24"  // Updated Kotlin version
}
