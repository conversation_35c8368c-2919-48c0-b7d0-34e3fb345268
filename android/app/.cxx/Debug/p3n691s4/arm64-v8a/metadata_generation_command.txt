                        -H/opt/homebrew/Caskroom/flutter/3.19.5/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=26
-D<PERSON>DROID_PLATFORM=android-26
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393
-DC<PERSON>KE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMA<PERSON>_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/StudioProjects/eye_help_second/build/app/intermediates/cxx/Debug/p3n691s4/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/StudioProjects/eye_help_second/build/app/intermediates/cxx/Debug/p3n691s4/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/StudioProjects/eye_help_second/android/app/.cxx/Debug/p3n691s4/arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2