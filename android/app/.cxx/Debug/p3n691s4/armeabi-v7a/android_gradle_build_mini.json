{"buildFiles": ["/opt/homebrew/Caskroom/flutter/3.19.5/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/eye_help_second/android/app/.cxx/Debug/p3n691s4/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/eye_help_second/android/app/.cxx/Debug/p3n691s4/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}